/* Make the documentation use full screen width */
.wy-nav-content {
    max-width: none !important;
    width: 100% !important;
    padding: 1.618em 3.236em !important;
}

/* Adjust the content wrapper - will be set by JavaScript */
.wy-nav-content-wrap {
    margin-left: 300px;
    transition: margin-left 0.2s ease;
    width: auto !important;
    position: relative !important;
    background: white !important;
    min-height: 100vh !important;
}

/* Make the main content area responsive */
.rst-content {
    max-width: none !important;
    width: 100% !important;
}

/* Optional: Adjust table widths to prevent overflow */
.rst-content table.docutils {
    width: 100% !important;
    table-layout: auto !important;
}

/* Optional: Better code block width handling */
.rst-content .highlight {
    width: 100% !important;
}

/* Content area positioning already handled above */

/* Optional: Improve readability with some margin on very wide screens */
@media (min-width: 1400px) {
    .wy-nav-content {
        max-width: none !important;
        margin: 0 auto !important;
    }
}

/* Resizable sidebar styles */
.wy-nav-side {
    position: fixed !important;
    top: 0 !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 300px;
    min-width: 200px;
    max-width: 600px;
    display: flex;
    flex-direction: column;
    z-index: 200 !important;
}

/* Ensure sidebar header (logo, search) adapts to width */
.wy-side-nav-search {
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 0.809em 0.809em !important;
}

.wy-side-nav-search input[type="text"] {
    width: 100% !important;
    box-sizing: border-box !important;
}

/* Make logo/title area responsive */
.wy-side-nav-search > div.version {
    width: 100% !important;
}

.wy-side-nav-search > a {
    width: 100% !important;
    display: block !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Responsive adjustments for narrow sidebar */
@media (max-width: 300px) {
    .wy-side-nav-search > a {
        font-size: 0.9em !important;
    }
    
    .wy-side-nav-search input[type="text"] {
        font-size: 0.8em !important;
    }
}

/* Ensure search input doesn't overflow */
.wy-side-nav-search form {
    width: 100% !important;
    margin: 0 !important;
}

/* Make search icon responsive */
.wy-side-nav-search .wy-dropdown {
    width: 100% !important;
}

/* Adjust search results dropdown width */
.wy-side-nav-search .wy-dropdown-menu {
    width: 100% !important;
    max-width: none !important;
    left: 0 !important;
    right: 0 !important;
}

/* Resize handle is created by JavaScript */

/* Make sure the sidebar content doesn't overflow */
.wy-side-scroll {
    width: 100% !important;
    flex: 1 !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    padding-right: 10px !important;
    box-sizing: border-box !important;
    scroll-behavior: auto !important; /* Prevent smooth scrolling on sidebar itself */
}

/* Ensure proper scroll behavior for main content area */
html {
    scroll-behavior: smooth !important;
}

/* Ensure anchor links work properly in main content */
.wy-nav-content-wrap {
    scroll-behavior: smooth !important;
}

/* Fix scroll to target for anchor links */
.rst-content {
    scroll-behavior: smooth !important;
}

/* Fix anchor scroll offset to account for fixed header */
.rst-content .section {
    scroll-margin-top: 60px;
}

/* Fix anchor scroll offset for headers */
.rst-content h1, .rst-content h2, .rst-content h3, .rst-content h4, .rst-content h5, .rst-content h6 {
    scroll-margin-top: 60px;
}

/* Fix anchor scroll offset for specific scroll targets */
.rst-content .headerlink {
    scroll-margin-top: 60px;
}

/* Fix sidebar navigation styling */
.wy-menu-vertical {
    width: 100% !important;
}

.wy-menu-vertical li {
    width: 100% !important;
}

.wy-menu-vertical a {
    width: 100% !important;
    word-wrap: break-word !important;
    white-space: normal !important;
}

/* Content area margin is handled by JavaScript */

/* Custom drag handle (more visible) */
.resize-handle {
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 100%;
    background: #ccc;
    cursor: col-resize;
    z-index: 1001;
    opacity: 0.3;
    transition: opacity 0.2s ease;
}

.resize-handle:hover {
    opacity: 0.8;
    background: #999;
}

.resize-handle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2px;
    height: 20px;
    background: #666;
    transform: translate(-50%, -50%);
    border-radius: 1px;
}

.resize-handle:hover::before {
    background: #333;
}

/* Ensure smooth resizing */
.wy-nav-side.resizing {
    user-select: none;
    pointer-events: none;
}

.wy-nav-side.resizing .wy-side-scroll {
    overflow: hidden;
}