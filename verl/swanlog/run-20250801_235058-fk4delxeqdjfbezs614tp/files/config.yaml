FRAMEWORK:
  desc: ''
  sort: 0
  value: verl
actor_rollout_ref:
  desc: ''
  sort: 1
  value:
    actor:
      _target_: verl.workers.config.FSDPActorConfig
      checkpoint:
        _target_: verl.trainer.config.CheckpointConfig
        async_save: false
        load_contents:
        - model
        - optimizer
        - extra
        save_contents:
        - model
        - optimizer
        - extra
      clip_ratio: 0.2
      clip_ratio_c: 3.0
      clip_ratio_high: 0.0004
      clip_ratio_low: 0.0003
      entropy_checkpointing: true
      entropy_coeff: 0
      entropy_from_logits_with_chunking: false
      fsdp_config:
        _target_: verl.workers.config.FSDPEngineConfig
        forward_prefetch: false
        fsdp_size: 8
        offload_policy: false
        optimizer_offload: false
        param_offload: false
        reshard_after_forward: true
        wrap_policy:
          min_num_params: 0
      grad_clip: 1.0
      kl_loss_coef: 0
      kl_loss_type: low_var_kl
      loss_agg_mode: seq-mean-token-mean
      optim:
        _target_: verl.workers.config.FSDPOptimizerConfig
        lr: 1.0e-06
        lr_warmup_steps: -1
        lr_warmup_steps_ratio: 0.0
        min_lr_ratio: 0.0
        num_cycles: 0.5
        total_training_steps: 300
        warmup_style: constant
        weight_decay: 0.01
      policy_loss:
        _target_: verl.workers.config.PolicyLossConfig
        clip_cov_lb: 1.0
        clip_cov_ratio: 0.0002
        clip_cov_ub: 5.0
        kl_cov_ratio: 0.0002
        loss_mode: gspo
        ppo_kl_coef: 0.1
      ppo_epochs: 1
      ppo_max_token_len_per_gpu: 16384
      ppo_micro_batch_size: null
      ppo_micro_batch_size_per_gpu: 1
      ppo_mini_batch_size: 128
      shuffle: false
      strategy: fsdp
      ulysses_sequence_parallel_size: 1
      use_dynamic_bsz: false
      use_fused_kernels: false
      use_kl_loss: false
      use_remove_padding: true
      use_torch_compile: true
    hybrid_engine: true
    model:
      custom_chat_template: null
      enable_activation_offload: false
      enable_gradient_checkpointing: true
      exclude_modules: null
      external_lib: null
      fused_kernel_options:
        impl_backend: torch
      lora_alpha: 16
      lora_rank: 0
      override_config: {}
      path: /share/tianyang/huggingface_model/Qwen/Qwen2.5-VL-7B-Instruct
      target_modules: all-linear
      trust_remote_code: false
      use_fused_kernels: false
      use_liger: true
      use_remove_padding: true
      use_shm: false
    profiler:
      _target_: verl.utils.profiler.ProfilerConfig
      all_ranks: false
      discrete: false
      ranks: []
    ref:
      entropy_checkpointing: false
      entropy_from_logits_with_chunking: false
      fsdp_config:
        _target_: verl.workers.config.FSDPEngineConfig
        forward_prefetch: false
        param_offload: true
        reshard_after_forward: true
        wrap_policy:
          min_num_params: 0
      log_prob_max_token_len_per_gpu: 16384
      log_prob_micro_batch_size: null
      log_prob_micro_batch_size_per_gpu: 10
      log_prob_use_dynamic_bsz: false
      strategy: fsdp
      ulysses_sequence_parallel_size: 1
      use_torch_compile: true
    rollout:
      agent:
        agent_loop_config_path: null
        custom_async_server:
          name: null
          path: null
        num_workers: 8
      calculate_log_probs: false
      disable_log_stats: true
      do_sample: true
      dtype: bfloat16
      enable_chunked_prefill: false
      enforce_eager: false
      engine_kwargs:
        sglang:
          attention_backend: null
        vllm:
          disable_mm_preprocessor_cache: false
          swap_space: null
      free_cache_engine: true
      gpu_memory_utilization: 0.9
      ignore_eos: false
      layered_summon: false
      load_format: dummy_dtensor
      log_prob_max_token_len_per_gpu: 16384
      log_prob_micro_batch_size: null
      log_prob_micro_batch_size_per_gpu: 10
      log_prob_use_dynamic_bsz: false
      max_model_len: null
      max_num_batched_tokens: 8192
      max_num_seqs: 1024
      mode: sync
      multi_stage_wake_up: false
      multi_turn:
        enable: false
        format: hermes
        interaction_config_path: null
        max_assistant_turns: null
        max_parallel_calls: 1
        max_tool_response_length: 256
        max_user_turns: null
        tokenization_sanity_check_mode: strict
        tool_config_path: null
        tool_response_truncate_side: middle
        use_inference_chat_template: false
      n: 8
      name: vllm
      prompt_length: 10240
      response_length: 10240
      temperature: 1.0
      tensor_model_parallel_size: 1
      top_k: -1
      top_p: 1
      trace:
        backend: null
        token2text: false
      update_weights_bucket_megabytes: 512
      val_kwargs:
        do_sample: false
        n: 1
        temperature: 0
        top_k: -1
        top_p: 1.0
algorithm:
  desc: ''
  sort: 7
  value:
    _target_: verl.trainer.config.AlgoConfig
    adv_estimator: grpo
    gamma: 1.0
    kl_ctrl:
      _target_: verl.trainer.config.KLControlConfig
      horizon: 10000
      kl_coef: 0
      target_kl: 0.1
      type: fixed
    kl_penalty: kl
    lam: 1.0
    norm_adv_by_std_in_grpo: true
    pf_ppo:
      reweight_method: pow
      weight_pow: 2.0
    use_kl_in_reward: false
    use_pf_ppo: false
critic:
  desc: ''
  sort: 4
  value:
    _target_: verl.workers.config.FSDPCriticConfig
    checkpoint:
      _target_: verl.trainer.config.CheckpointConfig
      async_save: false
      load_contents:
      - model
      - optimizer
      - extra
      save_contents:
      - model
      - optimizer
      - extra
    cliprange_value: 0.5
    enable: null
    forward_max_token_len_per_gpu: 32768
    forward_micro_batch_size: null
    forward_micro_batch_size_per_gpu: null
    grad_clip: 1.0
    loss_agg_mode: seq-mean-token-mean
    model:
      _target_: verl.workers.config.FSDPCriticModelCfg
      enable_activation_offload: false
      enable_gradient_checkpointing: true
      external_lib: null
      fsdp_config:
        _target_: verl.workers.config.FSDPEngineConfig
        forward_prefetch: false
        fsdp_size: -1
        offload_policy: false
        optimizer_offload: false
        param_offload: false
        reshard_after_forward: true
        wrap_policy:
          min_num_params: 0
      lora_alpha: 16
      lora_rank: 0
      override_config: {}
      path: ~/models/deepseek-llm-7b-chat
      target_modules: all-linear
      tokenizer_path: /share/tianyang/huggingface_model/Qwen/Qwen2.5-VL-7B-Instruct
      trust_remote_code: false
      use_remove_padding: false
      use_shm: false
    optim:
      _target_: verl.workers.config.FSDPOptimizerConfig
      lr: 1.0e-05
      lr_warmup_steps: -1
      lr_warmup_steps_ratio: 0.0
      min_lr_ratio: null
      total_training_steps: 300
      warmup_style: constant
      weight_decay: 0.01
    ppo_epochs: 1
    ppo_max_token_len_per_gpu: 32768
    ppo_micro_batch_size: null
    ppo_micro_batch_size_per_gpu: null
    ppo_mini_batch_size: 128
    profiler:
      _target_: verl.utils.profiler.ProfilerConfig
      all_ranks: false
      discrete: false
      ranks: []
    rollout_n: 8
    shuffle: false
    strategy: fsdp
    ulysses_sequence_parallel_size: 1
    use_dynamic_bsz: false
custom_reward_function:
  desc: ''
  sort: 6
  value:
    name: reward_fn
    path: /share/huangzihan/data/mmk12/reward.py
data:
  desc: ''
  sort: 3
  value:
    custom_cls:
      name: null
      path: null
    datagen:
      name: null
      path: null
    dataloader_num_workers: 8
    filter_overlong_prompts: false
    filter_overlong_prompts_workers: 1
    image_key: images
    max_prompt_length: 10240
    max_response_length: 10240
    prompt_key: prompt
    return_full_prompt: false
    return_multi_modal_inputs: true
    return_raw_chat: false
    return_raw_input_ids: false
    reward_fn_key: data_source
    sampler:
      class_name: null
      class_path: null
    shuffle: true
    tokenizer: null
    train_batch_size: 512
    train_files: /share/huangzihan/data/mmk12/train.parquet
    truncation: error
    trust_remote_code: false
    use_shm: false
    val_batch_size: null
    val_files: /share/huangzihan/data/mmk12/test.parquet
    validation_shuffle: false
    video_key: videos
ray_init:
  desc: ''
  sort: 8
  value:
    num_cpus: null
    timeline_json_file: null
reward_model:
  desc: ''
  sort: 5
  value:
    enable: false
    forward_max_token_len_per_gpu: 32768
    launch_reward_fn_async: false
    max_length: null
    micro_batch_size: null
    micro_batch_size_per_gpu: null
    model:
      external_lib: null
      fsdp_config:
        _target_: verl.workers.config.FSDPEngineConfig
        forward_prefetch: false
        fsdp_size: -1
        param_offload: false
        reshard_after_forward: true
        wrap_policy:
          min_num_params: 0
      input_tokenizer: /share/tianyang/huggingface_model/Qwen/Qwen2.5-VL-7B-Instruct
      path: ~/models/FsfairX-LLaMA3-RM-v0.1
      trust_remote_code: false
      use_fused_kernels: false
      use_remove_padding: false
      use_shm: false
    profiler:
      _target_: verl.utils.profiler.ProfilerConfig
      all_ranks: false
      discrete: false
      ranks: []
    reward_manager: dapo
    sandbox_fusion:
      max_concurrent: 64
      memory_limit_mb: 1024
      url: null
    strategy: fsdp
    ulysses_sequence_parallel_size: 1
    use_dynamic_bsz: false
trainer:
  desc: ''
  sort: 2
  value:
    balance_batch: true
    controller_nsight_options:
      cuda-graph-trace: graph
      cuda-memory-usage: 'true'
      trace: cuda,nvtx,cublas,ucx
    critic_warmup: 0
    default_hdfs_dir: null
    default_local_dir: /share/huangzihan/rl/baseline/verl_grpo_mmk12/qwen2_5_vl_7b_gspo
    del_local_ckpt_after_load: false
    device: cuda
    esi_redundant_time: 0
    experiment_name: qwen2_5_vl_7b_gspo
    log_val_generations: 0
    logger:
    - console
    - swanlab
    max_actor_ckpt_to_keep: null
    max_critic_ckpt_to_keep: null
    n_gpus_per_node: 8
    nnodes: 1
    npu_profile:
      options:
        analysis: true
        level: level1
        record_shapes: false
        roles:
        - all
        save_path: ./profiler_data
        with_cpu: true
        with_memory: false
        with_module: false
        with_npu: true
        with_stack: false
    profile_continuous_steps: false
    profile_steps: null
    project_name: verl_grpo_mmk12
    ray_wait_register_center_timeout: 300
    resume_from_path: null
    resume_mode: auto
    rollout_data_dir: /share/huangzihan/rl/baseline/verl_grpo_mmk12/qwen2_5_vl_7b_gspo/rollout
    save_freq: 20
    test_freq: 5
    total_epochs: 10
    total_training_steps: null
    use_legacy_worker_impl: auto
    val_before_train: false
    val_only: false
    validation_data_dir: /share/huangzihan/rl/baseline/verl_grpo_mmk12/qwen2_5_vl_7b_gspo/validation
    worker_nsight_options:
      capture-range: cudaProfilerApi
      capture-range-end: null
      cuda-graph-trace: graph
      cuda-memory-usage: 'true'
      kill: none
      trace: cuda,nvtx,cublas,ucx
