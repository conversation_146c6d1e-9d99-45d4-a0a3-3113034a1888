absl-py==2.3.1
accelerate==1.9.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.14
aiohttp-cors==0.8.1
aiosignal==1.4.0
airportsdata==20250706
aniso8601==10.0.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
apex==0.1
asciitree==0.3.3
astor==0.8.1
asttokens==3.0.0
async-timeout==5.0.1
attrs==25.3.0
audioread==3.0.1
av==15.0.0
binpacking==1.5.2
blake3==1.0.5
blinker==1.9.0
boto3==1.39.11
botocore==1.39.11
cachetools==5.5.2
certifi==2025.7.14
cffi==1.17.1
cfgv==3.4.0
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
cloudpickle==3.1.1
codetiming==1.4.0
colorama==0.4.6
colorful==0.5.7
compressed-tensors==0.9.3
contourpy==1.3.2
coverage==7.9.2
cupy-cuda12x==13.5.1
cycler==0.12.1
DataProperty==1.1.0
datasets==4.0.0
debugpy==1.8.15
decorator==5.2.1
decord==0.6.0
deepspeed==0.16.9
Deprecated==1.2.18
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distlib==0.4.0
distro==1.9.0
dnspython==2.7.0
docopt==0.6.2
editdistance==0.8.1
einops==0.8.1
email_validator==2.2.0
et_xmlfile==2.0.0
evaluate==0.4.5
exceptiongroup==1.3.0
executing==2.2.0
faiss-cpu==1.11.0.post1
fastapi==0.116.1
fastapi-cli==0.0.8
fastapi-cloud-cli==0.1.4
fasteners==0.19
fastrlock==0.8.3
filelock==3.18.0
flash_attn==2.7.4.post1
flashinfer-python==0.2.2.post1+cu124torch2.6
Flask==3.1.1
Flask-RESTful==0.3.10
fonttools==4.59.0
frozenlist==1.7.0
fsspec==2024.6.1
future==1.0.0
gguf==0.17.1
gitdb==4.0.12
GitPython==3.1.44
google-api-core==2.25.1
google-auth==2.40.3
googleapis-common-protos==1.70.0
grpcio==1.73.1
h11==0.16.0
hf_transfer==0.1.9
hf-xet==1.1.5
hjson==3.1.0
httpcore==1.0.9
httptools==0.6.4
httpx==0.28.1
huggingface-hub==0.33.4
hydra-core==1.3.2
icecream==2.1.5
identify==2.6.12
idna==3.10
imageio==2.37.0
importlib_metadata==8.0.0
iniconfig==2.1.0
interegular==0.3.3
itsdangerous==2.2.0
Jinja2==3.1.6
jiter==0.10.0
jmespath==1.0.1
joblib==1.5.1
json_repair==0.47.8
jsonlines==4.0.0
jsonschema==4.25.0
jsonschema-specifications==2025.4.1
kiwisolver==1.4.8
lark==1.2.2
latex2sympy2_extended==1.10.2
lazy_loader==0.4
librosa==0.11.0
liger_kernel==0.6.0
llguidance==0.7.30
llvmlite==0.44.0
lm-format-enforcer==0.10.11
loguru==0.7.3
lxml==6.0.0
Markdown==3.8.2
markdown-it-py==3.0.0
MarkupSafe==2.1.5
math-verify==0.8.0
mathruler==0.1.0
matplotlib==3.10.3
mbstrdecoder==1.1.4
mdurl==0.1.2
megatron-core==0.12.2
mistral_common==1.8.1
ml_dtypes==0.5.1
mpmath==1.3.0
msgpack==1.1.1
msgspec==0.19.0
multidict==6.6.3
multiprocess==0.70.16
munkres==1.1.4
nest-asyncio==1.6.0
networkx==3.3
ninja==********
nltk==3.9.1
nodeenv==1.9.1
num2words==0.5.14
numba==0.61.2
numcodecs==0.13.1
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-ml-py==12.575.51
nvidia-modelopt==0.33.0
nvidia-modelopt-core==0.33.0
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
omegaconf==2.3.0
openai==1.97.1
opencensus==0.11.4
opencensus-context==0.1.3
opencv-python==*********
opencv-python-headless==*********
openpyxl==3.1.5
opentelemetry-api==1.35.0
opentelemetry-exporter-otlp==1.26.0
opentelemetry-exporter-otlp-proto-common==1.26.0
opentelemetry-exporter-otlp-proto-grpc==1.26.0
opentelemetry-exporter-otlp-proto-http==1.26.0
opentelemetry-exporter-prometheus==0.56b0
opentelemetry-proto==1.26.0
opentelemetry-sdk==1.35.0
opentelemetry-semantic-conventions==0.56b0
opentelemetry-semantic-conventions-ai==0.4.11
optree==0.16.0
orjson==3.11.0
outlines==0.1.11
outlines_core==0.1.26
packaging==25.0
pandas==2.3.1
partial-json-parser==*******.post6
pathvalidate==3.3.1
peft==0.16.0
pillow==11.0.0
pip==25.1
platformdirs==4.3.8
pluggy==1.6.0
pooch==1.8.2
portalocker==3.2.0
pre_commit==4.2.0
prettytable==3.16.0
prometheus_client==0.22.1
prometheus-fastapi-instrumentator==7.1.0
propcache==0.3.2
proto-plus==1.26.1
protobuf==4.25.8
psutil==7.0.0
PuLP==3.2.1
py-cpuinfo==9.0.0
py-spy==0.4.1
pyarrow==21.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.2
pybind11==3.0.0
pycocoevalcap==1.2
pycocotools==2.0.10
pycountry==24.6.1
pycparser==2.22
pydantic==2.11.7
pydantic_core==2.33.2
pydantic-extra-types==2.10.5
pyecharts==2.0.8
pyext==0.7
Pygments==2.19.2
pylatexenc==2.10
PyMuPDF==1.26.3
pynvml==12.0.0
pyparsing==3.2.3
pytablewriter==1.2.1
pytest==8.4.1
pytest-cov==6.2.1
pytest-mock==3.14.1
pytest-random-order==1.2.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.1
python-json-logger==3.3.0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
pyzmq==27.0.0
qwen-vl-utils==0.0.11
ray==2.48.0
referencing==0.36.2
regex==2024.11.6
requests==2.32.4
rich==13.9.4
rich-toolkit==0.14.8
rignore==0.6.4
rpds-py==0.26.0
rsa==4.9.1
ruff==0.12.4
s3transfer==0.13.1
sacrebleu==2.5.1
safetensors==0.5.3
scikit-learn==1.7.1
scipy==1.15.3
sentencepiece==0.2.0
sentry-sdk==2.33.2
setuptools==78.1.1
shellingham==1.5.4
simplejson==3.20.1
six==1.17.0
smart_open==7.3.0.post1
smmap==5.0.2
sniffio==1.3.1
soundfile==0.13.1
soxr==0.5.0.post1
sqlitedict==2.1.0
starlette==0.47.2
sty==1.0.6
swankit==0.2.4
swanlab==0.6.7
sympy==1.13.1
tabledata==1.3.4
tabulate==0.9.0
tcolorpy==0.1.7
tenacity==9.1.2
tensorboard==2.20.0
tensorboard-data-server==0.7.2
tensordict==0.6.2
tensorstore==0.1.76
textdistance==4.6.3
threadpoolctl==3.6.0
tiktoken==0.9.0
timeout-decorator==0.5.0
timm==1.0.17
tokenizers==0.21.2
tomli==2.2.1
torch==2.6.0+cu124
torch_memory_saver==0.0.8
torch-tb-profiler==0.4.3
torchaudio==2.6.0+cu124
TorchCodec==0.2.1+cu124
torchdata==0.11.0
torchprofile==0.0.4
torchvision==0.21.0+cu124
tqdm==4.67.1
transformer_engine==2.5.0
transformer_engine_cu12==2.5.0
transformer_engine_torch==2.5.0
transformers==4.52.4
triton==3.2.0
typepy==1.3.4
typer==0.16.0
typing_extensions==4.12.2
typing-inspection==0.4.1
tzdata==2025.2
urllib3==2.5.0
uvicorn==0.35.0
uvloop==0.21.0
validators==0.35.0
virtualenv==20.32.0
vllm==0.8.5.post1
wandb==0.21.0
watchfiles==1.1.0
wcwidth==0.2.13
websockets==15.0.1
Werkzeug==3.1.3
wheel==0.45.1
wrapt==1.17.2
xformers==0.0.29.post2
xgrammar==0.1.18
xlsxwriter==3.2.5
xxhash==3.5.0
yarl==1.20.1
zarr==2.18.3
zipp==3.23.0
