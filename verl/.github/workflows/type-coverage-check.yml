name: Type Annotation and Docstring Coverage

on:
  pull_request:
    paths:
      - '**/*.py'

jobs:
  type-coverage-check:
    runs-on: [L20x8]
    timeout-minutes: 20 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: 1
      SGL_DISABLE_TP_MEMORY_INBALANCE_CHECK: "True"
    container:
      image: verlai/verl:app-verl0.5-sglang0.4.9.post4-mcore0.12.2-te2.2
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0  # 🚨 Important: fetch full history so `origin/main` is available
      - name: Install dependencies
        run: |
          pip install gitpython
          pip install -e .
      - name: Run type annotation coverage check
        run: |
          python3 tests/special_sanity/type_coverage_check.py
      - name: Run docstring coverage check
        run: |
          python3 tests/special_sanity/check_api_docs.py verl
