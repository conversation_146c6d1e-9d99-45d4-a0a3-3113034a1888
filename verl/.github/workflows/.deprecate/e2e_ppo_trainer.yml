name: e2e_ppo_trainer_deprecate

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  # For push, for now only anti-patterns are specified so it is more conservative
  # and achieves higher coverage.
  push:
    branches:
      - disabled_ci
  pull_request:
    branches:
      - disabled_ci
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!**/*.md"
      - "!docker/**"
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # Docs
      - "!docs/**"
      # Recipes
      - "!recipe/**"
      # Megatron
      - "!verl/workers/**/megatron_*.py"
      # Entrypoints
      - ".github/workflows/e2e_ppo_trainer.yml"
      - "examples/data_preprocess/gsm8k.py"
      - "examples/data_preprocess/geo3k.py"
      - "tests/special_e2e/ppo_trainer"
      - "verl/trainer/main_ppo.py"
      - "verl/trainer/config/ppo_trainer.yaml"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

jobs:
  pre_commit_for_ppo:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@0b93645e9fea7318ecaed2b359559ac225c90a2b # v5.3.0
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install the current repository
        run: |
          pip install -e .
      - name: Set ruff --output-format=github
        run: |
          sed -i 's/--output-format=full/--output-format=github/' .pre-commit-config.yaml
          git add .pre-commit-config.yaml
      - uses: pre-commit/action@v3.0.1
        with:
          extra_args: "" # Overriding default "--all-files"

  e2e_ppo_trainer_sglang_multiturn_with_tool:
    runs-on: [L20x8]
    needs: pre_commit_for_ppo
    timeout-minutes: 40 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    container:
      image: verlai/verl:app-verl0.5-sglang0.4.9.post4-mcore0.12.2-te2.2
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install -e .[test,gpu,sglang] --no-deps
      - name: Prepare gsm8k dataset with tool
        run: |
          ray stop --force
          python3 examples/data_preprocess/gsm8k_multiturn_w_tool.py --local_dir $HOME/data/gsm8k_verl_sgl_multi_turn_preprocessed
      - name: Running GSM8K with tool E2E training tests on 8 L20 GPUs with rmpad using function rm and save ckpt with sglang
        run: |
          ray stop --force
          bash tests/special_e2e/run_gsm8k_fsdp_sgl_multiturn_w_tool.sh
      - name: Running GSM8K with tool E2E training tests with FSDP2
        run: |
          ray stop --force
          FSDP_STRATEGY=fsdp2 bash tests/special_e2e/run_gsm8k_fsdp_sgl_multiturn_w_tool.sh

  e2e_ppo_trainer_sglang_vlm_multiturn_with_tool:
    runs-on: [L20x8]
    needs: pre_commit_for_ppo
    timeout-minutes: 40 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    container:
      image: verlai/verl:app-verl0.5-sglang0.4.9.post4-mcore0.12.2-te2.2
      options: --gpus all --shm-size=10g
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install -e .[test,geo,gpu,sglang]
      - name: Prepare geo3k dataset with tool
        run: |
          ray stop --force
          python3 examples/data_preprocess/geo3k_multiturn_w_tool.py --local_dir $HOME/data/geo3k_verl_sgl_multi_turn_preprocessed
      - name: Running GEO3K with tool E2E training tests on 8 L20 GPUs with rmpad using function rm and save ckpt with sglang
        run: |
          ray stop --force
          bash tests/special_e2e/run_geo3k_fsdp_sgl_multiturn_w_tool.sh
      - name: Running GEO3K with tool E2E training tests with FSDP2
        run: |
          ray stop --force
          FSDP_STRATEGY=fsdp2 bash tests/special_e2e/run_geo3k_fsdp_sgl_multiturn_w_tool.sh
