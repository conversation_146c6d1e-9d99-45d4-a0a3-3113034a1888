# # Tests layout

# Each folder under tests/ corresponds to a test category for a sub-namespace in verl. For instance:
# - `tests/trainer` for testing functionality related to `verl/trainer`
# - `tests/models` for testing functionality related to `verl/models`
# - ...

# There are a few folders with `special_` prefix, created for special purposes:
# - `special_distributed`: unit tests that must run with multiple GPUs
# - `special_e2e`: end-to-end tests with training/generation scripts
# - `special_npu`: tests for NPUs
# - `special_sanity`: a suite of quick sanity tests
# - `special_standalone`: a set of test that are designed to run in dedicated environments

# Accelerators for tests 
# - By default tests are run with GPU available, except for the ones under `special_npu`, and any test script whose name ends with `on_cpu.py`.
# - For test scripts with `on_cpu.py` name suffix would be tested on CPU resources in linux environment.

# # Workflow layout

# All CI tests are configured by yaml files in `.github/workflows/`. Here's an overview of all test configs:
# 1. A list of always triggered CPU sanity tests: `check-pr-title.yml`, `secrets_scan.yml`, `check-pr-title,yml`, `pre-commit.yml`, `doc.yml`
# 2. Some heavy multi-GPU unit tests, such as `model.yml`, `vllm.yml`, `sgl.yml`
# 3. End-to-end tests: `e2e_*.yml`
# 4. Unit tests
#   - `cpu_unit_tests.yml`, run pytest on all scripts with file name pattern `tests/**/test_*_on_cpu.py`
#   - `gpu_unit_tests.yml`, run pytest on all scripts with file without the `on_cpu.py` suffix.
#   - Since cpu/gpu unit tests by default runs all tests under `tests`, please make sure tests are manually excluded in them when
#     - new workflow yaml is added to `.github/workflows`
#     - new tests are added to workflow mentioned in 2.


name: e2e_eval_aime24

on:
  # Trigger the workflow on push or pull request,
  # but only for the main branch
  # For push, for now only anti-patterns are specified so it is more conservative
  # and achieves higher coverage.
  push:
    branches:
      - main
      - v0.*
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!*.md"
      - "!docker/**"
      - "!docs/**"
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      - "!recipe/**"
      - "recipe/r1"
      - "!recipe/r1/README.md"
  pull_request:
    branches:
      - main
    paths:
      - "**/*.py"
      # Other entrypoints
      - "!*.md"
      - "!docker/**"
      - "!docs/**"
      - "!examples/**"
      - "!tests/**"
      - "!verl/trainer/main_*.py"
      - "!verl/trainer/fsdp_sft_trainer.py"
      # Home
      - "recipe/r1"
      - "!recipe/r1/README.md"
      # Other recipes
      - "!recipe/**"
      # Entrypoints
      - ".github/workflows/e2e_eval_aime24.yml"
      - "tests/special_e2e/run_r1_distill_qwen_aime24_eval.sh"
      - "verl/trainer/main_generation.py"
      - "verl/trainer/config/generation.yaml"

# Cancel jobs on the same ref if a new one is triggered
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/main' }}

# Declare permissions just read content.
permissions:
  contents: read

env:
  IMAGE: "verl-ci-cn-beijing.cr.volces.com/verlai/verl:app-verl0.5-vllm0.9.1-mcore0.12.2-te2.2"
  DYNAMIC_RUNNER_ENDPOINT: "https://sd10g3clalm04ug7alq90.apigateway-cn-beijing.volceapi.com/runner"

jobs:
  setup:
    if: github.repository_owner == 'volcengine'
    runs-on: ubuntu-latest
    outputs:
      runner-label: ${{ steps.create-runner.outputs.runner-label }}
      mlp-task-id: ${{ steps.create-runner.outputs.mlp-task-id }}
    steps:
      - uses: actions/checkout@v4
      - id: create-runner
        uses: volcengine/vemlp-github-runner@v1 
        with:
          mode: "create"
          faas-url: "${{ env.DYNAMIC_RUNNER_ENDPOINT }}"
          mlp-image: "${{ env.IMAGE }}"
  
  e2e_eval_aime24:
    needs: setup
    runs-on: ["${{ needs.setup.outputs.runner-label || 'L20x8' }}"]
    timeout-minutes: 40 # Increase this timeout value as needed
    env:
      HTTP_PROXY: ${{ secrets.PROXY_HTTP }}
      HTTPS_PROXY: ${{ secrets.PROXY_HTTPS }}
      NO_PROXY: "localhost,127.0.0.1,hf-mirror.com"
      HF_ENDPOINT: "https://hf-mirror.com"
      HF_HUB_ENABLE_HF_TRANSFER: "0" # This is more stable
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          fetch-depth: 0
      - name: Install the current repository
        run: |
          pip3 install --no-deps -e .[test,gpu,math]
          pip3 install math-verify
      - name: Prepare aime24 dataset
        run: |
          ray stop --force
          python3 recipe/r1/data_process.py --task aime2024
      - name: Running generation and evaluation in AIME 2024
        run: |
          ray stop --force
          bash tests/special_e2e/run_r1_distill_qwen_aime24_eval.sh

  cleanup:
      runs-on: ubuntu-latest
      needs: [setup, e2e_eval_aime24]
      if: always()
      steps:
        - id: destroy-runner
          uses: volcengine/vemlp-github-runner@v1
          with:
            mode: "destroy"
            faas-url: "${{ env.DYNAMIC_RUNNER_ENDPOINT }}"
            mlp-task-id: "${{ needs.setup.outputs.mlp-task-id }}"