import pandas as pd

# 定义 Parquet 文件的路径
file_path = '/share/huangzihan/rollout_results.parquet'

# 读取 Parquet 文件
try:
    df = pd.read_parquet(file_path)

    # 打印处理后的 DataFrame 的第一行
    print("Parquet 文件中的第一条数据:")
    print("数据形状:", df.shape)
    print("列名:", df.columns.tolist())
    print("\n第一条数据:")
    
    # 修复：使用 iloc[0] 访问第一行
    first_row = df.iloc[0]
    
    # 打印除了images列之外的所有数据
    for column in df.columns:
        # if column == 'images':
        #     print(f"{column}: [图像数据 - 已省略显示]")
        # else:
        print(f"{column}: {first_row[column]}")

except FileNotFoundError:
    print(f"错误: 文件未找到，请确认路径是否正确: {file_path}")
except Exception as e:
    print(f"读取文件时发生错误: {e}")