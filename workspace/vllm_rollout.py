import pandas as pd
import json
from openai import AsyncOpenAI
import base64
from PIL import Image
from typing import List, Dict, Union
import argparse
import os
import time
import numpy as np
import asyncio
from tqdm.asyncio import tqdm
import datasets
from io import BytesIO
import copy

class VLLMRollout:
    def __init__(self, base_url: str = "http://localhost:8000/v1", model_name: str = "qwen2.5-vl"):
        """初始化vLLM客户端"""
        self.base_url = base_url
        self.model_name = model_name
        
        # 使用AsyncOpenAI库初始化异步客户端
        self.client = AsyncOpenAI(
            base_url=base_url,
            api_key="asd"
        )
        
        # 设置采样参数
        self.sampling_params = {
            "temperature": 1.0,  # 高温采样
            "top_p": 1,
            "max_tokens": 32768,
            "n": 5,  # 每个prompt采样5次
        }
    
    def encode_image_to_base64(self, image: Union[str, Image.Image, List[Dict], np.ndarray, bytes]) -> str:
        """将图片编码为base64，支持PIL图片对象、文件路径、字节数据或numpy数组"""
        try:
            # 如果是PIL图片对象，直接处理
            if isinstance(image, Image.Image):
                with BytesIO() as buffered:
                    img = image.convert('RGB') if image.mode != 'RGB' else image
                    img.save(buffered, format="PNG")
                    img_base64 = base64.b64encode(buffered.getvalue()).decode()
                return f"data:image/png;base64,{img_base64}"
            # 如果是bytes数据，直接编码
            elif isinstance(image, bytes):
                img_base64 = base64.b64encode(image).decode()
                return f"data:image/png;base64,{img_base64}"
            # 如果是字符串路径，加载图片
            elif isinstance(image, str):
                with Image.open(image) as img:
                    img = img.convert('RGB') if img.mode != 'RGB' else img
                    with BytesIO() as buffered:
                        img.save(buffered, format="PNG")
                        img_base64 = base64.b64encode(buffered.getvalue()).decode()
                return f"data:image/png;base64,{img_base64}"
            else:
                print(f"不支持的图片类型: {type(image)}")
                return ""
        except Exception as e:
            print(f"图片编码失败: {e}")
            return ""

    
    def load_train_data(self, parquet_path: str) -> datasets.Dataset:
        """加载parquet训练数据"""
        print(f"正在加载数据: {parquet_path}")
        dataset = datasets.Dataset.from_parquet(parquet_path)
        print(f"数据加载完成，共 {len(dataset)} 条记录")
        return dataset
    
    def format_messages(self, instruction: Union[str, List[Dict]], image: Union[str, Image.Image, List[Dict], np.ndarray, bytes, None] = None) -> List[Dict]:
        """格式化为OpenAI兼容的消息格式"""
        
        # 使用深拷贝避免修改原始数据
        messages = copy.deepcopy(instruction)
        
        # 如果有图片，需要在消息中查找并替换<image>标记
        if image is not None:
            image_base64 = self.encode_image_to_base64(image[0])
            if image_base64:
                for message in messages:
                    if message.get("role") == "user" and isinstance(message.get("content"), str):
                        content_text = message["content"]
                        if "<image>" in content_text:
                            # 将文本内容转换为多模态格式
                            text_parts = content_text.split("<image>")
                            content = []
                            
                            for i, text_part in enumerate(text_parts):
                                if text_part:
                                    content.append({"type": "text", "text": text_part})
                                if i < len(text_parts) - 1:
                                    content.append({"type": "image_url", "image_url": {"url": image_base64}})
                            
                            message["content"] = content
        
        return messages
    
    async def chat_completion(self, messages: List[Dict]) -> Dict:
        """异步调用OpenAI chat completion API"""
        try:
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_completion_tokens=10240,
                **self.sampling_params
            )
            return response
        except Exception as e:
            print(f"API请求失败: {e}")
            return {"error": str(e)}

    async def process_batch(self, messages_list: List[List[Dict]], max_concurrent: int = 16) -> List[Dict]:
        """异步批量处理messages"""
        print(f"开始处理 {len(messages_list)} 个对话...")
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        # 创建进度条
        progress_bar = tqdm(total=len(messages_list), desc="处理进度")
        
        async def process_single(i: int, messages: List[Dict]) -> Dict:
            async with semaphore:
                # 调用OpenAI API
                response = await self.chat_completion(messages)
                
                if "error" in response:
                    result = {
                        "messages": messages,
                        "samples": [],
                        "error": response["error"]
                    }
                else:
                    result = {
                        "messages": messages,
                        "samples": []
                    }
                    
                    # 收集5次采样结果
                    for j, choice in enumerate(response.choices):
                        result["samples"].append(choice.message.content.strip())
                
                # 更新进度条
                progress_bar.update(1)
                return result
        
        # 创建所有任务
        tasks = [process_single(i, messages) for i, messages in enumerate(messages_list)]
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks)
        
        # 关闭进度条
        progress_bar.close()
        
        return results
    
    async def run_rollout_async(self, parquet_path: str, output_path: str, 
                               instruction_col: str = "prompt", 
                               image_col: str = "images",
                               max_concurrent: int = 5):
        """异步执行完整的rollout流程"""
        
        # 加载数据
        dataset = self.load_train_data(parquet_path)
        
        # 准备messages
        messages_list = []
        original_data = []
        
        for idx, row in tqdm(enumerate(dataset)):
            instruction = row.get(instruction_col, "")
            image = row.get(image_col, None) if image_col in row else None
            original_data.append(row)
            messages = self.format_messages(instruction, image)
            messages_list.append(messages)
        
        # 一次性处理所有数据，通过信号量控制并发
        batch_results = await self.process_batch(messages_list, max_concurrent)
        
        # 合并原始数据和采样结果
        all_results = []
        for j, result in enumerate(batch_results):
            combined_result = {
                **original_data[j],
                "rollout_results": result['samples']
            }
            all_results.append(combined_result)
        
        # 保存结果
        self.save_results(all_results, output_path)
        print(f"采样完成！结果已保存到: {output_path}")
        
    
    def save_results(self, results: List[Dict], output_path: str):
        """保存采样结果为parquet格式"""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 使用datasets库保存，避免数据类型问题
        dataset = datasets.Dataset.from_list(results)
        dataset.to_parquet(output_path)

    def get_available_models(self) -> List[str]:
        """获取可用的模型列表"""
        try:
            print("正在获取可用模型列表...")
            models = self.client.models.list()
            model_names = [model.id for model in models.data]
            print(f"可用模型: {model_names}")
            return model_names
        except Exception as e:
            print(f"获取模型列表失败: {e}")
            return []

def main():
    parser = argparse.ArgumentParser(description="使用OpenAI库和vLLM在线服务进行高温采样")
    parser.add_argument("--parquet_path", type=str, default="/share/huangzihan/data/mmk12/train.parquet", help="输入的parquet文件路径")
    parser.add_argument("--output_path", type=str, default="/share/huangzihan/data/mmk12/train_72b_rollout.parquet", help="输出文件路径")
    parser.add_argument("--base_url", type=str, default="http://localhost:8000/v1", help="vLLM服务地址")
    parser.add_argument("--model_name", type=str, default="qwen2.5-vl", help="模型名称")
    parser.add_argument("--max_concurrent", type=int, default=32, help="最大并发数")
    
    args = parser.parse_args()
    
    # 创建rollout实例并运行
    rollout = VLLMRollout(base_url=args.base_url, model_name=args.model_name)
    asyncio.run(rollout.run_rollout_async(
        parquet_path=args.parquet_path,
        output_path=args.output_path,
        max_concurrent=args.max_concurrent
        ))




if __name__ == "__main__":
    main()
