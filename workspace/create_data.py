import re
import os
import datasets
import argparse
from PIL import Image
import qwen_vl_utils
def extract_solution(solution_str):
    final_solution = solution_str.replace('$', '')
    return final_solution

def check_image_size(image, min_factor=28):
    """检查图片尺寸是否满足最小要求"""
    if hasattr(image, 'size'):
        width, height = image.size
    elif hasattr(image, 'width') and hasattr(image, 'height'):
        width, height = image.width, image.height
    else:
        try:
            pil_image = Image.open(image) if isinstance(image, str) else image
            width, height = pil_image.size
        except:
            return False
    
    return height >= min_factor and width >= min_factor

def save_invalid_image(image, idx, split, save_dir):
    """保存不满足要求的图片"""
    try:
        if not os.path.exists(save_dir):
            os.makedirs(save_dir, exist_ok=True)
        
        # 获取图片尺寸信息
        size_info = "unknown"
        
        # 保存图片
        if hasattr(image, 'save'):
            # 直接是PIL Image对象
            size_info = f"{image.size[0]}x{image.size[1]}"
            image.save(os.path.join(save_dir, f"{split}_{idx}.png"))
        elif isinstance(image, str):
            # 图片路径
            pil_image = Image.open(image)
            size_info = f"{pil_image.size[0]}x{pil_image.size[1]}"
            pil_image.save(os.path.join(save_dir, f"{split}_{idx}.png"))
        else:
            # 其他类型，尝试转换
            pil_image = Image.fromarray(image) if hasattr(image, 'shape') else image
            if hasattr(pil_image, 'size'):
                size_info = f"{pil_image.size[0]}x{pil_image.size[1]}"
            pil_image.save(os.path.join(save_dir, f"{split}_{idx}.png"))
        
        print(f"保存无效图片: {split}_{idx}.png, 尺寸: {size_info}")
        return True
    except Exception as e:
        print(f"保存图片失败 {split}_{idx}: {e}")
        return False

SYSTEM_PROMPT = "Solve the question. The user asks a question, and you solves it. You first thinks about the reasoning process in the mind and then provides the user with the answer. The answer is in latex format. The final answer must be wrapped using the \\\\boxed{} command."

SYSTEM_PROMPT_TEST = "Solve the question. The user asks a question, and you solves it. You first thinks about the reasoning process in the mind and then provides the user with the answer. The answer is in latex format. The final answer must be wrapped using the \\\\boxed{} command. The final answer should be a single choice."
SYSTEM_PROMPT_32B = "Solve the question. The user asks a question, and you solves it. You first thinks about the reasoning process in the mind and then provides the user with the answer. The answer is in latex format. The final answer must be wrapped using the \\\\boxed{} command. The answer should be enclosed within <answer> </answer> tags, i.e., Since $1+1=2$, so the answer is $2$. <answer> The answer is $\\\\boxed{2}$ </answer>, which means the final answer assistant's output should start with <answer> and end with </answer>."
SYSTEM_PROMPT_7B = "Solve the question. The user asks a question, and you solves it. You first thinks about the reasoning process in the mind and then provides the user with the answer. The answer is in latex format and wrapped in $...$. The final answer must be wrapped using the \\\\boxed{} command. The reasoning process and answer are enclosed within <think> </think> and <answer> </answer> tags, respectively, i.e., <think> Since $1+1=2$, so the answer is $2$. </think><answer> The answer is $\\\\boxed{2}$ </answer>, which means assistant's output should start with <think> and end with </answer>."


# add a row to each data item that represents a unique id
def filter_valid_images(example, idx, split, invalid_images_dir):
    """过滤有效图片，保存无效图片"""
    image = example.get("image")
    if not check_image_size(image):
        # 保存不满足要求的图片
        try:
            save_invalid_image(image, idx, split, invalid_images_dir)
        except Exception as e:
            print(f"保存图片失败 {split}_{idx}: {e}")
        return False
    return True

def make_map_fn(split):
    def process_fn(example, idx):
        question = example.pop('question')
        question = '<image>'+ question
        answer = example.pop('answer')
        solution = extract_solution(answer)
        if split == 'train':
            system_prompt=SYSTEM_PROMPT
        elif split == 'test':
            system_prompt=SYSTEM_PROMPT_TEST
        data = {
            "data_source": data_source,
            "prompt": [{
                "role": "system", 
                "content": system_prompt
                },{
                "role": "user", 
                "content": question
            }],
            "images":  [example.pop("image")],
            "ability": example.pop("subject"),
            "reward_model": {
                "style": "rule",
                "ground_truth": solution
            },
            "extra_info": {
                'split': split,
                'index': idx
            }
        }
        return data

    return process_fn

if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--local_dir', default='/share/huangzihan/data/mmk12')
    parser.add_argument('--invalid_images_dir', default='/share/huangzihan/data/mmk12/invalid_images')
    args = parser.parse_args()

    num_few_shot = 5
    data_source = 'FanqingM/MMK12'

    dataset = datasets.load_dataset(data_source)

    train_dataset = dataset['train']
    test_dataset = dataset['test']

    invalid_images_dir = args.invalid_images_dir
    
    # 统计原始数量
    original_train_count = len(train_dataset)
    original_test_count = len(test_dataset)
    
    # 先过滤有效图片
    train_dataset = train_dataset.filter(lambda example, idx: filter_valid_images(example, idx, 'train', invalid_images_dir), with_indices=True, num_proc=16)
    test_dataset = test_dataset.filter(lambda example, idx: filter_valid_images(example, idx, 'test', invalid_images_dir), with_indices=True, num_proc=16)
    
    # 再进行数据转换
    train_dataset = train_dataset.map(function=make_map_fn('train'), with_indices=True, num_proc=64)
    test_dataset = test_dataset.map(function=make_map_fn('test'), with_indices=True, num_proc=64)
    
    print(f"训练集: 原始 {original_train_count} -> 过滤后 {len(train_dataset)}")
    print(f"测试集: 原始 {original_test_count} -> 过滤后 {len(test_dataset)}")
    print(f"无效图片保存在: {invalid_images_dir}")

    # 打印第一条训练数据
    if len(train_dataset) > 0:
        print("\n=== 第一条训练数据 ===")
        first_train_sample = train_dataset[0]
        for key, value in first_train_sample.items():
            if key == 'images':
                print(f"{key}: [PIL Image object] 尺寸: {value[0].size if hasattr(value[0], 'size') else 'unknown'}")
            else:
                print(f"{key}: {value}")
    
    # 打印第一条测试数据
    if len(test_dataset) > 0:
        print("\n=== 第一条测试数据 ===")
        first_test_sample = test_dataset[0]
        for key, value in first_test_sample.items():
            if key == 'images':
                print(f"{key}: [PIL Image object] 尺寸: {value[0].size if hasattr(value[0], 'size') else 'unknown'}")
            else:
                print(f"{key}: {value}")

    local_dir = args.local_dir
    train_dataset.to_parquet(os.path.join(local_dir, 'train.parquet'))
    test_dataset.to_parquet(os.path.join(local_dir, 'test.parquet'))
    
    print(f"\n数据已保存到: {local_dir}")
