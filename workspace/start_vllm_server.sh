#!/bin/bash

# 设置模型路径
MODEL_PATH=${1:-"/share/tianyang/huggingface_model/Qwen/Qwen2.5-VL-72B-Instruct"}
PORT=${2:-8000}
HOST=${3:-"127.0.0.1"}


# 启动vLLM OpenAI兼容服务
python -m vllm.entrypoints.openai.api_server \
    --model $MODEL_PATH \
    --port $PORT \
    --host $HOST \
    --tensor-parallel-size 4 \
    --data-parallel-size 2 \
    --limit-mm-per-prompt image=10 \
    --served-model-name qwen2.5-vl \
    --disable-mm-preprocessor-cache \
