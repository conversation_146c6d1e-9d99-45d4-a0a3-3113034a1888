set -xeuo pipefail
cd /share/huangzihan/verl


project_name=${project_name:-verl_grpo_mmk12}
experiment_name=${experiment_name:-qwen2_5_vl_7b_mmk12_first_epoch_72b_rollout}

nnodes=${VC_WORKER_NUM:-1}
n_gpus_per_node=${MA_NUM_GPUS:-8}

train_files=${train_files:-/share/huangzihan/data/mmk12/train_72b_rollout.parquet}
val_files=${val_files:-/share/huangzihan/data/mmk12/test.parquet}
train_batch_size=${train_batch_size:-512}
max_prompt_length=${max_prompt_length:-10240}
max_response_length=${max_response_length:-16384}
filter_overlong_prompts=${filter_overlong_prompts:-false}
truncation=${truncation:-error}

adv_estimator=${adv_estimator:-grpo}
loss_mode=${loss_mode:-gspo}
loss_agg_mode=${loss_agg_mode:-seq-mean-token-mean}
use_kl_in_reward=${use_kl_in_reward:-false}

reward_manager=${reward_manager:-dapo}

model_path=${model_path:-/share/tianyang/huggingface_model/Qwen/Qwen2.5-VL-7B-Instruct}
use_liger=${use_liger:-true}
use_fused_kernels=${use_fused_kernels:-false} #这一项不要动，永远设置为false

lr=${lr:-1e-6}
weight_decay=${weight_decay:-0.01}
lr_warmup_steps=${lr_warmup_steps:--1}
lr_warmup_steps_ratio=${lr_warmup_steps_ratio:-0.0}
min_lr_ratio=${min_lr_ratio:-0.0}
num_cycles=${num_cycles:-0.5}
warmup_style=${warmup_style:-constant}

ppo_mini_batch_size=${ppo_mini_batch_size:-128}
ppo_micro_batch_size_per_gpu=${ppo_micro_batch_size_per_gpu:-1}
clip_ratio_low=${clip_ratio_low:-0.0003}
clip_ratio_high=${clip_ratio_high:-0.0004}
clip_ratio_c=${clip_ratio_c:-3.0}
use_kl_loss=${use_kl_loss:-true}
kl_loss_coef=${kl_loss_coef:-0.01}
kl_loss_type=${kl_loss_type:-low_var_kl}
entropy_coeff=${entropy_coeff:-0}
ulysses_sequence_parallel_size=${ulysses_sequence_parallel_size:-1}
fsdp_size=${fsdp_size:-$n_gpus_per_node}
actor_offload=${actor_offload:-false}
use_dynamic_bsz=${use_dynamic_bsz:-true}
entropy_checkpointing=${entropy_checkpointing:-true}

rollout_name=${rollout_name:-vllm}
log_prob_micro_batch_size_per_gpu=${log_prob_micro_batch_size_per_gpu:-10}
tensor_model_parallel_size=${tensor_model_parallel_size:-1}
gpu_memory_utilization=${gpu_memory_utilization:-0.9}
enable_chunked_prefill=${enable_chunked_prefill:-false}
enforce_eager=${enforce_eager:-false}
free_cache_engine=${free_cache_engine:-true}
n=${n:-5}
actor_ppo_max_token_len=${actor_ppo_max_token_len:-$(((max_prompt_length + max_response_length) * 2))}
infer_ppo_max_token_len=${infer_ppo_max_token_len:-$(((max_prompt_length + max_response_length) * 3))}

ref_offload=${ref_offload:-true}

# logger=${logger:-['console', 'swanlab']}

save_freq=${save_freq:-20}
test_freq=${test_freq:-5}
total_epochs=${total_epochs:-10}
default_local_dir=${default_local_dir:-/share/huangzihan/rl/baseline/${project_name}/${experiment_name}}
rollout_data_dir=${rollout:-/share/huangzihan/rl/baseline/${project_name}/${experiment_name}/rollout}
validation_data_dir=${validation_data_dir:-/share/huangzihan/rl/baseline/${project_name}/${experiment_name}/validation}

reward_fn_name=reward_fn
reward_fn_file_path=/share/huangzihan/data/mmk12/reward.py


python3 -m  verl.trainer.main_ppo \
    data.train_files=${train_files} \
    data.val_files=${val_files} \
    data.train_batch_size=${train_batch_size} \
    data.max_prompt_length=${max_prompt_length} \
    data.max_response_length=${max_response_length} \
    data.filter_overlong_prompts=${filter_overlong_prompts} \
    data.truncation=${truncation} \
    data.image_key=images \
    algorithm.adv_estimator=${adv_estimator} \
    algorithm.use_kl_in_reward=${use_kl_in_reward} \
    actor_rollout_ref.actor.policy_loss.loss_mode=${loss_mode} \
    actor_rollout_ref.actor.loss_agg_mode=${loss_agg_mode} \
    reward_model.reward_manager=${reward_manager} \
    actor_rollout_ref.model.path=${model_path} \
    actor_rollout_ref.model.use_remove_padding=True \
    actor_rollout_ref.model.enable_gradient_checkpointing=True \
    actor_rollout_ref.model.use_liger=${use_liger} \
    actor_rollout_ref.model.use_fused_kernels=${use_fused_kernels} \
    actor_rollout_ref.actor.optim.lr=${lr} \
    actor_rollout_ref.actor.optim.weight_decay=${weight_decay} \
    actor_rollout_ref.actor.optim.lr_warmup_steps=${lr_warmup_steps} \
    actor_rollout_ref.actor.optim.lr_warmup_steps_ratio=${lr_warmup_steps_ratio} \
    actor_rollout_ref.actor.optim.min_lr_ratio=${min_lr_ratio} \
    actor_rollout_ref.actor.optim.num_cycles=${num_cycles} \
    actor_rollout_ref.actor.optim.warmup_style=${warmup_style} \
    actor_rollout_ref.actor.ppo_mini_batch_size=${ppo_mini_batch_size} \
    actor_rollout_ref.actor.ppo_micro_batch_size_per_gpu=${ppo_micro_batch_size_per_gpu} \
    actor_rollout_ref.actor.clip_ratio_low=${clip_ratio_low} \
    actor_rollout_ref.actor.clip_ratio_high=${clip_ratio_high} \
    actor_rollout_ref.actor.clip_ratio_c=${clip_ratio_c} \
    actor_rollout_ref.actor.use_kl_loss=${use_kl_loss} \
    actor_rollout_ref.actor.kl_loss_coef=${kl_loss_coef} \
    actor_rollout_ref.actor.kl_loss_type=${kl_loss_type} \
    actor_rollout_ref.actor.entropy_coeff=${entropy_coeff} \
    actor_rollout_ref.actor.use_dynamic_bsz=${use_dynamic_bsz} \
    actor_rollout_ref.actor.ppo_max_token_len_per_gpu=${actor_ppo_max_token_len} \
    actor_rollout_ref.actor.entropy_checkpointing=${entropy_checkpointing} \
    actor_rollout_ref.actor.fsdp_config.fsdp_size=${fsdp_size} \
    actor_rollout_ref.actor.fsdp_config.param_offload=${actor_offload} \
    actor_rollout_ref.actor.fsdp_config.optimizer_offload=${actor_offload} \
    actor_rollout_ref.rollout.name=${rollout_name} \
    actor_rollout_ref.rollout.tensor_model_parallel_size=${tensor_model_parallel_size} \
    actor_rollout_ref.rollout.log_prob_micro_batch_size_per_gpu=${log_prob_micro_batch_size_per_gpu} \
    actor_rollout_ref.rollout.gpu_memory_utilization=${gpu_memory_utilization} \
    actor_rollout_ref.rollout.enable_chunked_prefill=${enable_chunked_prefill} \
    actor_rollout_ref.rollout.enforce_eager=${enforce_eager} \
    actor_rollout_ref.rollout.free_cache_engine=${free_cache_engine} \
    actor_rollout_ref.rollout.n=${n} \
    actor_rollout_ref.rollout.log_prob_use_dynamic_bsz=${use_dynamic_bsz} \
    actor_rollout_ref.rollout.log_prob_max_token_len_per_gpu=${infer_ppo_max_token_len} \
    actor_rollout_ref.ref.log_prob_micro_batch_size_per_gpu=${log_prob_micro_batch_size_per_gpu} \
    actor_rollout_ref.ref.log_prob_use_dynamic_bsz=${use_dynamic_bsz} \
    actor_rollout_ref.ref.log_prob_max_token_len_per_gpu=${infer_ppo_max_token_len} \
    actor_rollout_ref.ref.fsdp_config.param_offload=${ref_offload} \
    custom_reward_function.path="${reward_fn_file_path}"\
    custom_reward_function.name="${reward_fn_name}"\
    trainer.critic_warmup=0 \
    trainer.logger=['console','swanlab'] \
    trainer.project_name=${project_name} \
    trainer.experiment_name=${experiment_name} \
    trainer.n_gpus_per_node=${n_gpus_per_node} \
    trainer.val_before_train=false \
    trainer.nnodes=${nnodes} \
    trainer.save_freq=${save_freq} \
    trainer.test_freq=${test_freq} \
    trainer.total_epochs=${total_epochs} \
    trainer.default_local_dir=${default_local_dir} \
    trainer.rollout_data_dir=${rollout_data_dir} \
    trainer.validation_data_dir=${validation_data_dir} $@